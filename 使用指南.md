# 🎯 ASR-LLM-TTS 配置化使用指南

## 🎉 恭喜！环境配置完成

你的ASR-LLM-TTS项目已经完全配置好了，包括：
- ✅ **8张RTX 4090 GPU** 自动管理
- ✅ **模型路径** 自动配置 (`/pic/suziren/models/`)
- ✅ **智能GPU选择** (推荐使用GPU 1，最空闲)
- ✅ **所有依赖** 已安装完成

## 🚀 快速开始

### 方法1: 使用启动器 (推荐)

```bash
# 激活环境
conda activate cosyvoice
cd /home/<USER>/suziren/ASR-LLM-TTS

# 启动Web界面 (最简单)
python launcher.py --script webui --port 8000

# 启动实时语音对话
python launcher.py --script realtime

# 指定GPU启动
python launcher.py --script webui --gpu 1 --port 8000

# 查看GPU状态
python launcher.py --list-gpus

# 预览命令 (不实际运行)
python launcher.py --script webui --dry-run
```

### 方法2: 直接运行

```bash
# Web界面
python webui.py --port 8000 --gpu 1

# 实时对话 (配置化版本)
python realtime_chat_configured.py

# 原版实时对话 (需要手动修改路径)
python 13_SenceVoice_QWen2.5_edgeTTS_realTime.py
```

## 📋 支持的脚本

| 脚本名称 | 功能描述 | 推荐场景 |
|---------|----------|----------|
| `webui` | Web图形界面 | 🌟 新手首选，操作简单 |
| `realtime` | 实时语音对话 | 🌟 核心功能，语音交互 |
| `cosyvoice` | 高质量TTS版本 | 追求音质 |
| `multimodal` | 音视频多模态 | 需要视觉理解 |
| `voiceprint` | 声纹识别版本 | 多人对话 |
| `test-asr` | 测试语音识别 | 调试ASR |
| `test-llm` | 测试大语言模型 | 调试LLM |
| `test-tts` | 测试语音合成 | 调试TTS |

## 🖥️ GPU使用策略

系统会自动选择最空闲的GPU，当前推荐：

```
GPU 1: 最空闲 (413MB/24GB, 0%使用率) ⭐ 推荐
GPU 4: 较空闲 (3221MB/24GB, 33%使用率)
GPU 0: 中等负载 (3915MB/24GB, 0%使用率)
```

## 🎯 模型配置

所有模型路径已自动配置：

```
📁 /pic/suziren/models/
├── 🎤 asr/
│   └── SenseVoiceSmall/          # 语音识别
├── 🧠 llm/
│   └── Qwen2.5-1.5B-Instruct/    # 大语言模型
├── 🎵 tts/
│   ├── CosyVoice-300M/           # 语音合成
│   ├── CosyVoice-300M-SFT/       # 高质量TTS
│   └── CosyVoice-ttsfrd/         # 文本处理版
├── 🔊 vad/
│   └── speech_fsmn_vad_zh-cn-16k-common-pytorch/
└── 🎯 kws/
    └── speech_charctc_kws_phone-xiaoyun/
```

## 🌐 Web界面使用

启动Web界面后，访问: `http://localhost:8000`

功能包括：
- 🎵 **文本转语音** - 输入文字生成语音
- 🎤 **语音克隆** - 3秒极速复刻音色
- 🌍 **跨语种复刻** - 支持多语言
- 🎛️ **自然语言控制** - 用文字描述想要的音色

## 🎙️ 实时对话使用

启动实时对话后：
1. 🎤 **开始说话** - 系统自动录音
2. ⏸️ **停顿1秒** - 触发语音识别
3. 🧠 **AI思考** - 大语言模型生成回答
4. 🔊 **语音播放** - 自动播放AI回答

## 🔧 高级配置

### 修改默认模型

编辑 `config.py`:

```python
class ProjectConfig:
    # 修改默认模型
    DEFAULT_ASR_MODEL = "SenseVoiceSmall"      # 语音识别
    DEFAULT_LLM_MODEL = "Qwen2.5-1.5B"        # 大语言模型  
    DEFAULT_TTS_MODEL = "CosyVoice-300M-SFT"  # 语音合成
```

### 添加新模型

```python
class ModelConfig:
    # 添加新的LLM模型
    LLM_MODELS = {
        "Qwen2.5-1.5B": f"{BASE_MODEL_DIR}/llm/Qwen2.5-1.5B-Instruct",
        "Qwen2.5-7B": f"{BASE_MODEL_DIR}/llm/Qwen2.5-7B-Instruct",  # 新增
        # ... 其他模型
    }
```

## 🐛 故障排除

### 1. GPU内存不足
```bash
# 查看GPU状态
python launcher.py --list-gpus

# 指定空闲GPU
python launcher.py --script webui --gpu 1
```

### 2. 模型路径错误
```bash
# 测试配置
python config.py

# 检查模型文件
ls -la /pic/suziren/models/llm/Qwen2.5-1.5B-Instruct/
```

### 3. 依赖问题
```bash
# 重新激活环境
conda activate cosyvoice

# 检查关键包
python -c "import torch; print(torch.__version__)"
python -c "from transformers import AutoModel; print('✅ transformers OK')"
python -c "from cosyvoice.cli.cosyvoice import CosyVoice; print('✅ cosyvoice OK')"
```

### 4. 音频设备问题
```bash
# 安装音频依赖
conda activate cosyvoice
pip install sounddevice

# 测试音频设备
python -c "import sounddevice as sd; print(sd.query_devices())"
```

## 📊 性能优化建议

1. **GPU选择**: 使用GPU 1 (最空闲)
2. **模型选择**: Qwen2.5-1.5B (速度快，显存占用少)
3. **批处理**: Web界面支持批量处理
4. **内存管理**: 长时间运行建议重启

## 🎯 推荐使用流程

### 新手入门
1. `python launcher.py --script webui` - 启动Web界面
2. 在浏览器中测试文本转语音
3. 尝试语音克隆功能

### 进阶使用
1. `python launcher.py --script realtime` - 实时语音对话
2. 测试语音识别准确性
3. 调整对话参数

### 高级应用
1. `python launcher.py --script multimodal` - 多模态交互
2. `python launcher.py --script voiceprint` - 声纹识别
3. 自定义模型配置

## 🎉 开始使用吧！

现在一切都配置好了，选择你喜欢的方式开始体验：

```bash
# 最简单 - Web界面
python launcher.py --script webui

# 最实用 - 实时对话  
python launcher.py --script realtime

# 最智能 - 多模态交互
python launcher.py --script multimodal
```

祝你使用愉快！🚀
