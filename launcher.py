#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASR-LLM-TTS 项目启动器
自动选择GPU并启动相应的脚本
"""

import os
import sys
import argparse
import subprocess
from config import GPUManager, ProjectConfig, print_gpu_status, get_model_path

def main():
    parser = argparse.ArgumentParser(description="ASR-LLM-TTS 项目启动器")
    parser.add_argument("--script", "-s", required=False,
                       help="要运行的脚本名称 (如: webui, realtime, cosyvoice)")
    parser.add_argument("--gpu", "-g", type=int, default=None,
                       help="指定GPU ID (默认自动选择)")
    parser.add_argument("--port", "-p", type=int, default=8000,
                       help="Web界面端口 (仅webui使用)")
    parser.add_argument("--list-gpus", action="store_true",
                       help="显示GPU状态并退出")
    parser.add_argument("--dry-run", action="store_true",
                       help="只显示将要执行的命令，不实际运行")

    args = parser.parse_args()

    # 如果只是查看GPU状态
    if args.list_gpus:
        print_gpu_status()
        return

    # 如果没有指定脚本，显示帮助
    if not args.script:
        parser.print_help()
        print("\n支持的脚本:")
        script_map = {
            "webui": "webui.py - Web图形界面",
            "realtime": "13_SenceVoice_QWen2.5_edgeTTS_realTime.py - 实时语音对话",
            "cosyvoice": "10_SenceVoice_QWen2.5_cosyVoice.py - 高质量TTS版本",
            "multimodal": "14_SenceVoice_QWen2VL_edgeTTS_realTime.py - 多模态交互",
            "voiceprint": "15.1_SenceVoice_kws_CAM++.py - 声纹识别版本"
        }
        for key, desc in script_map.items():
            print(f"  {key:<12} - {desc}")
        return
    
    # 设置环境
    ProjectConfig.setup_environment()
    
    # 脚本映射
    script_map = {
        "webui": "webui.py",
        "realtime": "13_SenceVoice_QWen2.5_edgeTTS_realTime.py",
        "cosyvoice": "10_SenceVoice_QWen2.5_cosyVoice.py",
        "multimodal": "14_SenceVoice_QWen2VL_edgeTTS_realTime.py",
        "voiceprint": "15.1_SenceVoice_kws_CAM++.py",
        "test-asr": "6_Inference_funasr.py",
        "test-llm": "0_Inference_QWen2.5.py",
        "test-tts": "1_Inference_CosyVoice.py"
    }
    
    if args.script not in script_map:
        print(f"❌ 不支持的脚本: {args.script}")
        print(f"支持的脚本: {', '.join(script_map.keys())}")
        return 1
    
    script_file = script_map[args.script]
    
    # 检查脚本文件是否存在
    if not os.path.exists(script_file):
        print(f"❌ 脚本文件不存在: {script_file}")
        return 1
    
    # 显示GPU状态
    print_gpu_status()
    
    # 选择GPU
    if args.gpu is not None:
        if args.gpu < 0 or args.gpu > 7:
            print("❌ GPU ID必须在0-7之间")
            return 1
        gpu_id = args.gpu
        print(f"🎯 用户指定使用GPU {gpu_id}")
    else:
        gpu_id = GPUManager.get_best_gpu(min_memory_gb=6.0)
        if gpu_id is None:
            print("❌ 没有找到合适的GPU")
            return 1
        print(f"🤖 自动选择GPU {gpu_id}")
    
    # 设置GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
    
    # 构建命令
    cmd = [
        "conda", "run", "-n", "cosyvoice",
        "python", script_file
    ]
    
    # 为webui添加端口参数
    if args.script == "webui":
        cmd.extend(["--port", str(args.port)])
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.getcwd()}:{env.get('PYTHONPATH', '')}"
    env['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
    
    print(f"\n🚀 启动脚本: {script_file}")
    print(f"🖥️  使用GPU: {gpu_id}")
    if args.script == "webui":
        print(f"🌐 Web端口: {args.port}")
        print(f"🔗 访问地址: http://localhost:{args.port}")
    
    if args.dry_run:
        print(f"\n📋 将要执行的命令:")
        print(f"   {' '.join(cmd)}")
        print(f"📋 环境变量:")
        print(f"   CUDA_VISIBLE_DEVICES={gpu_id}")
        print(f"   PYTHONPATH={env['PYTHONPATH']}")
        return 0
    
    print(f"\n" + "="*60)
    print(f"开始运行...")
    print("="*60)
    
    try:
        # 运行脚本
        result = subprocess.run(cmd, env=env, cwd=os.getcwd())
        return result.returncode
    except KeyboardInterrupt:
        print(f"\n\n⏹️  用户中断程序")
        return 0
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
