#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置化的实时语音对话脚本
基于13_SenceVoice_QWen2.5_edgeTTS_realTime.py修改
"""

import cv2
import pyaudio
import wave
import threading
import numpy as np
import time
from queue import Queue
import webrtcvad
import os
import threading
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
from funasr import AutoModel
import pygame
import edge_tts
import asyncio
from time import sleep
import langid
from langdetect import detect

# 导入配置
from config import ModelConfig, GPUManager, ProjectConfig, get_model_path, print_gpu_status

# 参数设置
AUDIO_RATE = 16000        # 音频采样率
AUDIO_CHANNELS = 1        # 单声道
CHUNK = 1024              # 音频块大小
VAD_MODE = 3              # VAD 模式 (0-3, 数字越大越敏感)
OUTPUT_DIR = "./output"   # 输出目录
NO_SPEECH_THRESHOLD = 1   # 无效语音阈值，单位：秒
folder_path = "./Test_QWen2_VL/"
audio_file_count = 0

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(folder_path, exist_ok=True)

# 队列用于音频和视频同步缓存
audio_queue = Queue()
video_queue = Queue()

# 全局变量
last_active_time = time.time()
recording_active = True
segments_to_save = []
saved_intervals = []
last_vad_end_time = 0  # 上次保存的 VAD 有效段结束时间

def setup_models():
    """设置和加载模型"""
    print("🔧 正在设置模型...")
    
    # 设置环境
    ProjectConfig.setup_environment()
    
    # 显示GPU状态
    print_gpu_status()
    
    # 设置GPU
    gpu_id = GPUManager.set_gpu(min_memory_gb=8.0)
    
    try:
        # 加载ASR模型
        print("📢 加载语音识别模型...")
        asr_model_path = get_model_path('asr', 'SenseVoiceSmall')
        model_senceVoice = AutoModel(model=asr_model_path, trust_remote_code=True)
        print(f"✅ ASR模型加载成功: {asr_model_path}")
        
        # 加载LLM模型
        print("🧠 加载大语言模型...")
        llm_model_path = get_model_path('llm', 'Qwen2.5-1.5B')
        model = AutoModelForCausalLM.from_pretrained(
            llm_model_path,
            torch_dtype="auto",
            device_map="auto"
        )
        tokenizer = AutoTokenizer.from_pretrained(llm_model_path)
        print(f"✅ LLM模型加载成功: {llm_model_path}")
        
        return model_senceVoice, model, tokenizer
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print("尝试使用默认路径...")
        
        # 回退到默认路径
        try:
            model_senceVoice = AutoModel(model="iic/SenseVoiceSmall", trust_remote_code=True)
            model = AutoModelForCausalLM.from_pretrained(
                "Qwen/Qwen2.5-1.5B-Instruct",
                torch_dtype="auto",
                device_map="auto"
            )
            tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2.5-1.5B-Instruct")
            print("✅ 使用在线模型加载成功")
            return model_senceVoice, model, tokenizer
        except Exception as e2:
            print(f"❌ 在线模型也加载失败: {e2}")
            raise

# EdgeTTS语音合成
async def text_to_speech_edge(text, output_file="output.wav", voice="zh-CN-XiaoxiaoNeural"):
    """使用EdgeTTS进行语音合成"""
    communicate = edge_tts.Communicate(text, voice)
    await communicate.save(output_file)

def play_audio(file_path):
    """播放音频文件"""
    try:
        pygame.mixer.init()
        pygame.mixer.music.load(file_path)
        pygame.mixer.music.play()
        while pygame.mixer.music.get_busy():
            pygame.time.wait(100)
    except Exception as e:
        print(f"播放音频出错: {e}")

def audio_callback(indata, frames, time, status):
    """音频回调函数"""
    global last_active_time, recording_active
    if recording_active:
        audio_queue.put(indata.copy())
        last_active_time = time.inputBufferAdcTime

def process_audio_stream():
    """处理音频流"""
    global last_active_time, recording_active, segments_to_save, saved_intervals, last_vad_end_time
    
    vad = webrtcvad.Vad(VAD_MODE)
    audio_buffer = []
    silence_duration = 0
    speech_detected = False
    
    while recording_active:
        if not audio_queue.empty():
            audio_chunk = audio_queue.get()
            audio_buffer.append(audio_chunk)
            
            # 转换为VAD所需的格式
            audio_int16 = (audio_chunk * 32767).astype(np.int16)
            audio_bytes = audio_int16.tobytes()
            
            # VAD检测
            try:
                is_speech = vad.is_speech(audio_bytes, AUDIO_RATE)
                
                if is_speech:
                    speech_detected = True
                    silence_duration = 0
                else:
                    silence_duration += len(audio_chunk) / AUDIO_RATE
                
                # 如果检测到语音后有足够的静音，保存音频段
                if speech_detected and silence_duration > NO_SPEECH_THRESHOLD:
                    if len(audio_buffer) > 0:
                        save_audio_segment(audio_buffer)
                    
                    audio_buffer = []
                    speech_detected = False
                    silence_duration = 0
                    
            except Exception as e:
                print(f"VAD检测出错: {e}")
        
        time.sleep(0.01)

def save_audio_segment(audio_buffer):
    """保存音频段并进行处理"""
    global audio_file_count
    
    if len(audio_buffer) == 0:
        return
    
    # 合并音频数据
    audio_data = np.concatenate(audio_buffer, axis=0)
    
    # 保存为WAV文件
    audio_file_count += 1
    filename = f"{folder_path}audio_{audio_file_count:04d}.wav"
    
    # 使用wave模块保存
    with wave.open(filename, 'wb') as wf:
        wf.setnchannels(AUDIO_CHANNELS)
        wf.setsampwidth(2)  # 16-bit
        wf.setframerate(AUDIO_RATE)
        audio_int16 = (audio_data * 32767).astype(np.int16)
        wf.writeframes(audio_int16.tobytes())
    
    print(f"💾 保存音频: {filename}")
    
    # 处理音频
    process_saved_audio(filename)

def process_saved_audio(audio_file):
    """处理保存的音频文件"""
    try:
        # 语音识别
        print("🎤 正在进行语音识别...")
        res = model_senceVoice.generate(
            input=audio_file,
            cache={},
            language="auto",
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15,
        )
        
        if res and len(res) > 0 and 'text' in res[0]:
            user_input = res[0]['text']
            print(f"👤 用户说: {user_input}")
            
            if user_input.strip():
                # 大语言模型推理
                print("🧠 正在思考...")
                messages = [
                    {"role": "system", "content": "你是一个有用的AI助手。请用简洁、友好的方式回答用户的问题。"},
                    {"role": "user", "content": user_input}
                ]
                
                text = tokenizer.apply_chat_template(
                    messages,
                    tokenize=False,
                    add_generation_prompt=True
                )
                
                model_inputs = tokenizer([text], return_tensors="pt").to(model.device)
                
                with torch.no_grad():
                    generated_ids = model.generate(
                        **model_inputs,
                        max_new_tokens=512,
                        do_sample=True,
                        temperature=0.7,
                        top_p=0.9
                    )
                
                generated_ids = [
                    output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
                ]
                
                response = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
                print(f"🤖 AI回答: {response}")
                
                # 语音合成
                print("🎵 正在合成语音...")
                output_audio = f"{OUTPUT_DIR}/response_{audio_file_count:04d}.wav"
                asyncio.run(text_to_speech_edge(response, output_audio))
                
                # 播放音频
                print("🔊 播放回答...")
                play_audio(output_audio)
                
        else:
            print("⚠️  没有识别到有效语音")
            
    except Exception as e:
        print(f"❌ 处理音频时出错: {e}")

def main():
    """主函数"""
    global recording_active, model_senceVoice, model, tokenizer
    
    print("🎙️  实时语音对话系统")
    print("=" * 50)
    
    # 设置和加载模型
    model_senceVoice, model, tokenizer = setup_models()
    
    print("\n🎯 系统就绪！")
    print("💡 说话后停顿1秒以上，系统会自动处理")
    print("💡 按Ctrl+C退出")
    print("=" * 50)
    
    # 启动音频处理线程
    audio_thread = threading.Thread(target=process_audio_stream, daemon=True)
    audio_thread.start()
    
    # 启动音频录制
    try:
        import sounddevice as sd
        with sd.InputStream(
            samplerate=AUDIO_RATE,
            channels=AUDIO_CHANNELS,
            callback=audio_callback,
            blocksize=CHUNK
        ):
            print("🎤 开始录音...")
            while True:
                time.sleep(1)
                
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断，正在退出...")
        recording_active = False
        
    except Exception as e:
        print(f"❌ 录音出错: {e}")
        recording_active = False
    
    print("👋 再见！")

if __name__ == "__main__":
    main()
