name: QWen
channels:
  - conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - defaults
dependencies:
  - bzip2=1.0.8=h2bbff1b_6
  - ca-certificates=2024.9.24=haa95532_0
  - cairo=1.16.0=hc68a040_5
  - dlfcn-win32=1.4.1=h63175ca_0
  - expat=2.6.3=he0c23c2_0
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_3
  - fontconfig=2.14.1=hb33846d_3
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - freetype=2.12.1=ha860e81_0
  - fribidi=1.0.10=h8d14728_0
  - getopt-win32=0.1=hcfcfb64_1
  - glib=2.78.4=hd77b12b_0
  - glib-tools=2.78.4=hd77b12b_0
  - graphite2=1.3.13=h63175ca_1003
  - graphviz=7.1.0=h51cb2cd_0
  - gts=0.7.6=h6b5321d_4
  - harfbuzz=6.0.0=h196d34a_1
  - icu=72.1=h63175ca_0
  - jbig=2.1=h8d14728_2003
  - jpeg=9e=hcfcfb64_3
  - lerc=2.2.1=h0e60522_0
  - libdeflate=1.7=h8ffe710_5
  - libexpat=2.6.3=he0c23c2_0
  - libffi=3.4.4=hd77b12b_1
  - libgd=2.3.3=hd77b12b_3
  - libglib=2.78.4=ha17d25a_0
  - libiconv=1.17=hcfcfb64_2
  - libpng=1.6.39=h8cc25b3_0
  - libtiff=4.3.0=h0c97f57_1
  - libwebp=1.2.4=hcfcfb64_3
  - libwebp-base=1.2.4=h8ffe710_0
  - libxml2=2.13.1=h24da03e_2
  - lz4-c=1.9.3=h8ffe710_1
  - openfst=1.8.3=hc790b64_3
  - openssl=3.3.2=h2466b09_0
  - pango=1.50.12=hdffb7b3_1
  - pcre2=10.42=h0ff8eda_1
  - pip=24.2=py310haa95532_0
  - pixman=0.43.4=h63175ca_0
  - pynini=2.1.6=py310h232114e_0
  - python=3.10.15=h4607a30_1
  - python_abi=3.10=2_cp310
  - setuptools=75.1.0=py310haa95532_0
  - sqlite=3.45.3=h2bbff1b_0
  - tk=8.6.14=h0416ee5_0
  - ucrt=10.0.22621.0=h57928b3_1
  - vc=14.40=h2eaa2aa_1
  - vc14_runtime=14.40.33810=hcc2c482_22
  - vs2015_runtime=14.40.33810=h3bf8584_22
  - wheel=0.44.0=py310haa95532_0
  - xz=5.4.6=h8cc25b3_1
  - zlib=1.2.13=h8cc25b3_1
  - zstd=1.5.0=h6255e5f_0
  - pip:
      - absl-py==2.1.0
      - accelerate==0.33.0
      - addict==2.4.0
      - aiofiles==23.2.1
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.10.10
      - aiosignal==1.3.1
      - alembic==1.13.3
      - aliyun-python-sdk-core==2.16.0
      - aliyun-python-sdk-kms==2.16.5
      - altair==5.4.1
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.6.2.post1
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - asttokens==2.4.1
      - async-lru==2.0.4
      - async-timeout==4.0.3
      - attrs==24.2.0
      - audioread==3.0.1
      - auto-gptq==0.7.1
      - autopage==0.5.2
      - babel==2.16.0
      - beautifulsoup4==4.12.3
      - bibtexparser==2.0.0b7
      - bleach==6.1.0
      - certifi==2024.8.30
      - cffi==1.17.1
      - cfgv==3.4.0
      - charset-normalizer==3.4.0
      - click==8.1.7
      - cliff==4.7.0
      - clldutils==3.23.1
      - cmaes==0.11.1
      - cmd2==2.5.0
      - colorama==0.4.6
      - coloredlogs==15.0.1
      - colorlog==6.8.2
      - comm==0.2.2
      - comtypes==1.4.8
      - conformer==0.3.2
      - contourpy==1.3.0
      - crcmod==1.7
      - cryptography==43.0.3
      - csvw==3.5.1
      - cycler==0.12.1
      - cython==3.0.11
      - datasets==2.18.0
      - debugpy==1.8.7
      - decorator==5.1.1
      - defusedxml==0.7.1
      - diffusers==0.29.2
      - dill==0.3.8
      - distlib==0.3.9
      - dlinfo==1.2.1
      - edge-tts==6.1.17
      - editdistance==0.8.1
      - einops==0.8.0
      - einx==0.3.0
      - exceptiongroup==1.2.2
      - executing==2.1.0
      - fastapi==0.115.3
      - fastjsonschema==2.20.0
      - ffmpy==0.4.0
      - filelock==3.13.1
      - flatbuffers==24.3.25
      - fonttools==4.54.1
      - fqdn==1.5.1
      - frozendict==2.4.6
      - frozenlist==1.5.0
      - fsspec==2024.2.0
      - funasr==1.1.12
      - gast==0.6.0
      - gdown==5.2.0
      - gekko==1.2.1
      - gradio==3.43.2
      - gradio-client==0.5.0
      - greenlet==3.1.1
      - grpcio==1.67.0
      - h11==0.14.0
      - html5lib-modern==1.2
      - httpcore==1.0.6
      - httpx==0.27.2
      - huggingface-hub==0.26.1
      - humanfriendly==10.0
      - hydra-colorlog==1.2.0
      - hydra-core==1.3.2
      - hydra-optuna-sweeper==1.2.0
      - hyperpyyaml==1.2.2
      - identify==2.6.1
      - idna==3.10
      - importlib-metadata==8.5.0
      - importlib-resources==6.4.5
      - inflect==7.4.0
      - iniconfig==2.0.0
      - intel-openmp==2021.4.0
      - ipykernel==6.29.5
      - ipython==8.29.0
      - ipywidgets==8.1.5
      - isodate==0.7.2
      - isoduration==20.11.0
      - jaconv==0.4.0
      - jamo==0.4.1
      - jedi==0.19.1
      - jieba==0.42.1
      - jinja2==3.1.3
      - jmespath==0.10.0
      - joblib==1.4.2
      - json5==0.9.25
      - jsonpointer==3.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - jupyter-client==8.6.3
      - jupyter-core==5.7.2
      - jupyter-events==0.10.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.14.2
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.2.5
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - jupyterlab-widgets==3.0.13
      - kaldiio==2.18.0
      - kiwisolver==1.4.7
      - language-tags==1.2.0
      - lazy-loader==0.4
      - librosa==0.10.2.post1
      - lightning==2.2.4
      - lightning-utilities==0.11.8
      - llvmlite==0.43.0
      - loguru==0.7.2
      - lxml==5.3.0
      - mako==1.3.6
      - markdown==3.7
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - matcha-tts==*******
      - matplotlib==3.9.2
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mistune==3.0.2
      - mkl==2021.4.0
      - modelscope==1.15.0
      - more-itertools==10.5.0
      - mpmath==1.3.0
      - msgpack==1.1.0
      - multidict==6.1.0
      - multiprocess==0.70.16
      - narwhals==1.10.0
      - natsort==8.4.0
      - nbclient==0.10.0
      - nbconvert==7.16.4
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.2.1
      - nodeenv==1.9.1
      - notebook==7.2.2
      - notebook-shim==0.2.4
      - numba==0.60.0
      - numpy==1.26.3
      - omegaconf==2.3.0
      - onnxruntime==1.19.2
      - openai-whisper==20231117
      - optimum==1.23.2
      - optimum-habana==1.14.0
      - optuna==2.10.1
      - orjson==3.10.10
      - oss2==2.19.1
      - overrides==7.7.0
      - packaging==24.1
      - pandas==2.2.3
      - pandocfilters==1.5.1
      - parso==0.8.4
      - pbr==6.1.0
      - peft==0.13.2
      - phonemizer==3.3.0
      - pillow==10.2.0
      - platformdirs==4.3.6
      - pluggy==1.5.0
      - pooch==1.8.2
      - pre-commit==4.0.1
      - prettytable==3.11.0
      - prometheus-client==0.21.0
      - prompt-toolkit==3.0.48
      - propcache==0.2.0
      - protobuf==5.28.3
      - psutil==6.1.0
      - pure-eval==0.2.3
      - pyarrow==17.0.0
      - pyarrow-hotfix==0.6
      - pycparser==2.22
      - pycryptodome==3.21.0
      - pydantic==2.9.2
      - pydantic-core==2.23.4
      - pydub==0.25.1
      - pygame==2.6.1
      - pygments==2.18.0
      - pylatexenc==2.10
      - pynndescent==0.5.13
      - pyparsing==3.2.0
      - pyperclip==1.9.0
      - pypiwin32==223
      - pyreadline3==3.5.4
      - pysocks==1.7.1
      - pytest==8.3.3
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - python-json-logger==2.0.7
      - python-multipart==0.0.12
      - pytorch-lightning==2.4.0
      - pytorch-wpe==0.0.1
      - pyttsx3==2.98
      - pytz==2024.2
      - pywin32==308
      - pywinpty==2.0.14
      - pyyaml==6.0.2
      - pyzmq==26.2.0
      - rdflib==7.1.0
      - referencing==0.35.1
      - regex==2024.9.11
      - requests==2.32.3
      - rfc3339-validator==0.1.4
      - rfc3986==1.5.0
      - rfc3986-validator==0.1.1
      - rich==13.9.3
      - rootutils==1.0.7
      - rouge==1.0.1
      - rpds-py==0.20.0
      - ruamel-yaml==0.18.6
      - ruamel-yaml-clib==0.2.12
      - ruff==0.7.1
      - safetensors==0.4.5
      - scikit-learn==1.5.2
      - scipy==1.14.1
      - seaborn==0.13.2
      - segments==2.2.1
      - semantic-version==2.10.0
      - send2trash==1.8.3
      - sentence-transformers==3.0.1
      - sentencepiece==0.2.0
      - shellingham==1.5.4
      - simplejson==3.19.3
      - six==1.16.0
      - sniffio==1.3.1
      - sortedcontainers==2.4.0
      - sounddevice==0.5.1
      - soundfile==0.12.1
      - soupsieve==2.6
      - soxr==0.5.0.post1
      - sqlalchemy==2.0.36
      - stack-data==0.6.3
      - starlette==0.41.0
      - stevedore==5.3.0
      - sympy==1.13.1
      - tabulate==0.9.0
      - tbb==2021.11.0
      - tensorboard==2.18.0
      - tensorboard-data-server==0.7.2
      - tensorboardx==*******
      - terminado==0.18.1
      - threadpoolctl==3.5.0
      - tiktoken==0.8.0
      - tinycss2==1.4.0
      - tokenizers==0.20.1
      - tomli==2.0.2
      - tomlkit==0.12.0
      - torch==2.3.1+cu118
      - torch-complex==0.4.4
      - torchaudio==2.3.1+cu118
      - torchmetrics==1.5.1
      - torchvision==0.18.1+cu118
      - tornado==6.4.1
      - tqdm==4.66.5
      - traitlets==5.14.3
      - transformers==4.45.2
      - typeguard==4.3.0
      - typer==0.12.5
      - types-python-dateutil==2.9.0.20241003
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - umap-learn==0.5.6
      - unidecode==1.3.8
      - uri-template==1.3.0
      - uritemplate==4.1.1
      - urllib3==2.2.3
      - uvicorn==0.32.0
      - vector-quantize-pytorch==1.18.5
      - virtualenv==20.27.0
      - wcwidth==0.2.13
      - webcolors==24.8.0
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - websockets==11.0.3
      - werkzeug==3.0.6
      - wetextprocessing==*******
      - wget==3.2
      - whisper==1.1.10
      - widgetsnbextension==4.0.13
      - win32-setctime==1.1.0
      - xxhash==3.5.0
      - yapf==0.40.2
      - yarl==1.16.0
      - zipp==3.20.2
prefix: E:\2_PYTHON\Anaconda\envs\QWen
