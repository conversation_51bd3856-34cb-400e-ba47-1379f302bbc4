#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASR-LLM-TTS 项目配置文件
包含模型路径、GPU选择等配置
"""

import os
import torch
import subprocess
import json
from typing import Dict, List, Tuple, Optional

class ModelConfig:
    """模型路径配置"""
    
    # 基础模型目录
    BASE_MODEL_DIR = "/pic/suziren/models"
    
    # ASR 模型路径
    ASR_MODELS = {
        "SenseVoiceSmall": f"{BASE_MODEL_DIR}/asr/SenseVoiceSmall",
        "Paraformer": f"{BASE_MODEL_DIR}/asr/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-online",
        "ParaformerVAD": f"{BASE_MODEL_DIR}/asr/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
    }
    
    # LLM 模型路径
    LLM_MODELS = {
        "Qwen2.5-1.5B": f"{BASE_MODEL_DIR}/llm/Qwen2.5-1.5B-Instruct",
        "Qwen2-Audio-7B": f"{BASE_MODEL_DIR}/llm/Qwen2-Audio-7B-Instruct",
        "Qwen-Audio-Chat": f"{BASE_MODEL_DIR}/llm/Qwen-Audio-Chat"
    }
    
    # TTS 模型路径
    TTS_MODELS = {
        "CosyVoice-300M": f"{BASE_MODEL_DIR}/tts/CosyVoice-300M",
        "CosyVoice-300M-SFT": f"{BASE_MODEL_DIR}/tts/CosyVoice-300M-SFT",
        "CosyVoice-ttsfrd": f"{BASE_MODEL_DIR}/tts/CosyVoice-ttsfrd"
    }
    
    # VAD 模型路径
    VAD_MODELS = {
        "FSMN-VAD": f"{BASE_MODEL_DIR}/vad/speech_fsmn_vad_zh-cn-16k-common-pytorch"
    }
    
    # KWS 模型路径
    KWS_MODELS = {
        "CharCTC-KWS": f"{BASE_MODEL_DIR}/kws/speech_charctc_kws_phone-xiaoyun"
    }

class GPUManager:
    """GPU管理器"""
    
    @staticmethod
    def get_gpu_info() -> List[Dict]:
        """获取GPU信息"""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=index,name,memory.used,memory.total,utilization.gpu',
                                   '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True, check=True, timeout=10)

            gpu_info = []
            for line in result.stdout.strip().split('\n'):
                parts = line.split(', ')
                if len(parts) >= 5:
                    gpu_info.append({
                        'index': int(parts[0]),
                        'name': parts[1],
                        'memory_used': int(parts[2]),
                        'memory_total': int(parts[3]),
                        'utilization': int(parts[4])
                    })
            return gpu_info
        except subprocess.TimeoutExpired:
            print("获取GPU信息超时")
            return []
        except Exception as e:
            print(f"获取GPU信息失败: {e}")
            return []
    
    @staticmethod
    def get_best_gpu(min_memory_gb: float = 8.0) -> Optional[int]:
        """
        获取最适合的GPU
        Args:
            min_memory_gb: 最小内存要求(GB)
        Returns:
            最适合的GPU索引，如果没有合适的返回None
        """
        gpu_info = GPUManager.get_gpu_info()
        if not gpu_info:
            return None
        
        # 计算每个GPU的可用内存和负载分数
        suitable_gpus = []
        for gpu in gpu_info:
            memory_available_gb = (gpu['memory_total'] - gpu['memory_used']) / 1024
            if memory_available_gb >= min_memory_gb:
                # 负载分数 = 内存使用率 + GPU使用率
                load_score = (gpu['memory_used'] / gpu['memory_total']) * 100 + gpu['utilization']
                suitable_gpus.append((gpu['index'], load_score, memory_available_gb))
        
        if not suitable_gpus:
            print(f"没有找到满足{min_memory_gb}GB内存要求的GPU")
            return None
        
        # 选择负载最低的GPU
        best_gpu = min(suitable_gpus, key=lambda x: x[1])
        return best_gpu[0]
    
    @staticmethod
    def set_gpu(gpu_id: Optional[int] = None, min_memory_gb: float = 8.0) -> int:
        """
        设置使用的GPU
        Args:
            gpu_id: 指定的GPU ID，如果为None则自动选择
            min_memory_gb: 最小内存要求
        Returns:
            实际使用的GPU ID
        """
        if gpu_id is None:
            gpu_id = GPUManager.get_best_gpu(min_memory_gb)
            if gpu_id is None:
                print("警告: 没有找到合适的GPU，使用GPU 0")
                gpu_id = 0
        
        # 设置CUDA设备
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        torch.cuda.set_device(0)  # 因为CUDA_VISIBLE_DEVICES设置后，可见的GPU变成0
        
        print(f"✅ 使用GPU {gpu_id}")
        if torch.cuda.is_available():
            print(f"   设备名称: {torch.cuda.get_device_name(0)}")
            print(f"   显存总量: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        return gpu_id

class ProjectConfig:
    """项目配置"""
    
    # 默认模型选择
    DEFAULT_ASR_MODEL = "SenseVoiceSmall"
    DEFAULT_LLM_MODEL = "Qwen2.5-1.5B"
    DEFAULT_TTS_MODEL = "CosyVoice-300M-SFT"
    
    # 音频参数
    AUDIO_RATE = 16000
    AUDIO_CHANNELS = 1
    CHUNK = 1024
    
    # VAD参数
    VAD_MODE = 3
    NO_SPEECH_THRESHOLD = 1
    
    # 输出目录
    OUTPUT_DIR = "./output"
    TEMP_DIR = "./temp"
    
    # HuggingFace镜像
    HF_ENDPOINT = "https://hf-mirror.com"
    
    @staticmethod
    def setup_environment():
        """设置环境变量"""
        os.environ['HF_ENDPOINT'] = ProjectConfig.HF_ENDPOINT
        os.makedirs(ProjectConfig.OUTPUT_DIR, exist_ok=True)
        os.makedirs(ProjectConfig.TEMP_DIR, exist_ok=True)

def get_model_path(model_type: str, model_name: str) -> str:
    """
    获取模型路径
    Args:
        model_type: 模型类型 ('asr', 'llm', 'tts', 'vad', 'kws')
        model_name: 模型名称
    Returns:
        模型路径
    """
    model_maps = {
        'asr': ModelConfig.ASR_MODELS,
        'llm': ModelConfig.LLM_MODELS,
        'tts': ModelConfig.TTS_MODELS,
        'vad': ModelConfig.VAD_MODELS,
        'kws': ModelConfig.KWS_MODELS
    }
    
    if model_type not in model_maps:
        raise ValueError(f"不支持的模型类型: {model_type}")
    
    if model_name not in model_maps[model_type]:
        raise ValueError(f"找不到模型: {model_name} in {model_type}")
    
    model_path = model_maps[model_type][model_name]
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型路径不存在: {model_path}")
    
    return model_path

def print_gpu_status():
    """打印GPU状态"""
    gpu_info = GPUManager.get_gpu_info()
    if not gpu_info:
        print("❌ 无法获取GPU信息")
        return
    
    print("\n🖥️  GPU状态:")
    print("=" * 80)
    print(f"{'GPU':<4} {'名称':<20} {'已用内存':<10} {'总内存':<10} {'使用率':<8} {'可用内存':<10}")
    print("-" * 80)
    
    for gpu in gpu_info:
        available_memory = gpu['memory_total'] - gpu['memory_used']
        print(f"{gpu['index']:<4} {gpu['name']:<20} {gpu['memory_used']:<10} "
              f"{gpu['memory_total']:<10} {gpu['utilization']:<8}% {available_memory:<10}")
    
    print("=" * 80)
    
    # 推荐最佳GPU
    best_gpu = GPUManager.get_best_gpu()
    if best_gpu is not None:
        print(f"💡 推荐使用GPU {best_gpu}")
    print()

if __name__ == "__main__":
    # 测试配置
    print("🔧 ASR-LLM-TTS 配置测试")
    print("=" * 50)
    
    # 打印GPU状态
    print_gpu_status()
    
    # 测试模型路径
    print("📁 模型路径测试:")
    try:
        asr_path = get_model_path('asr', 'SenseVoiceSmall')
        print(f"✅ ASR模型: {asr_path}")
    except Exception as e:
        print(f"❌ ASR模型: {e}")
    
    try:
        llm_path = get_model_path('llm', 'Qwen2.5-1.5B')
        print(f"✅ LLM模型: {llm_path}")
    except Exception as e:
        print(f"❌ LLM模型: {e}")
    
    try:
        tts_path = get_model_path('tts', 'CosyVoice-300M-SFT')
        print(f"✅ TTS模型: {tts_path}")
    except Exception as e:
        print(f"❌ TTS模型: {e}")
