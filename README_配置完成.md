# 🎉 ASR-LLM-TTS 配置完成！

## ✅ 配置完成清单

### 🏗️ 环境配置
- ✅ **Conda环境**: `cosyvoice` (Python 3.8.15)
- ✅ **PyTorch**: 2.3.1+cu121 (支持CUDA 12.8)
- ✅ **核心依赖**: 所有必需包已安装
- ✅ **GPU支持**: 8张RTX 4090自动管理

### 📁 模型配置
- ✅ **ASR模型**: SenseVoiceSmall (`/pic/suziren/models/asr/SenseVoiceSmall`)
- ✅ **LLM模型**: Qwen2.5-1.5B (`/pic/suziren/models/llm/Qwen2.5-1.5B-Instruct`)
- ✅ **TTS模型**: CosyVoice-300M-SFT (`/pic/suziren/models/tts/CosyVoice-300M-SFT`)
- ✅ **路径自动化**: 无需手动修改代码中的路径

### 🔧 智能工具
- ✅ **配置文件**: `config.py` - 统一管理所有模型路径
- ✅ **启动器**: `launcher.py` - 智能GPU选择和脚本启动
- ✅ **GPU管理**: 自动选择最空闲的GPU (推荐GPU 1)

## 🚀 立即开始使用

### 🌟 方式1: Web界面 (推荐新手)

```bash
# 激活环境并启动
conda activate cosyvoice
cd /home/<USER>/suziren/ASR-LLM-TTS

# 启动Web界面
python launcher.py --script webui

# 然后在浏览器访问: http://localhost:8000
```

### 🎙️ 方式2: 实时语音对话 (推荐进阶)

```bash
# 启动实时对话
python launcher.py --script realtime

# 开始说话，停顿1秒后系统自动处理
```

### 🎯 方式3: 指定GPU启动

```bash
# 查看GPU状态
python launcher.py --list-gpus

# 指定GPU 1启动Web界面
python launcher.py --script webui --gpu 1 --port 8000
```

## 📋 所有可用脚本

| 脚本名称 | 命令 | 功能描述 |
|---------|------|----------|
| **webui** | `python launcher.py --script webui` | 🌟 Web图形界面，最适合新手 |
| **realtime** | `python launcher.py --script realtime` | 🌟 实时语音对话，核心功能 |
| **cosyvoice** | `python launcher.py --script cosyvoice` | 高质量TTS版本 |
| **multimodal** | `python launcher.py --script multimodal` | 音视频多模态交互 |
| **voiceprint** | `python launcher.py --script voiceprint` | 声纹识别版本 |

## 🖥️ GPU使用情况

当前GPU状态 (自动选择最优):
```
GPU 1: ⭐ 推荐使用 (413MB/24GB, 0%使用率)
GPU 4: 备选 (3221MB/24GB, 31%使用率)  
GPU 0: 备选 (3937MB/24GB, 0%使用率)
```

## 🎯 核心功能演示

### 1. Web界面功能
- 📝 **文本转语音**: 输入文字，生成高质量语音
- 🎤 **语音克隆**: 3秒音频即可复刻音色
- 🌍 **多语言支持**: 中英文跨语种合成
- 🎛️ **音色控制**: 自然语言描述想要的音色

### 2. 实时对话功能
- 🎤 **语音识别**: SenseVoice高精度识别
- 🧠 **智能对话**: Qwen2.5大模型理解和回答
- 🔊 **语音合成**: EdgeTTS自然语音播放
- ⚡ **实时交互**: 说话停顿1秒自动处理

### 3. 多模态功能
- 👁️ **视觉理解**: 可以看图片和视频
- 🎙️ **语音交互**: 结合视觉的语音对话
- 🎥 **实时视频**: 摄像头实时视频理解

## 🔧 高级配置

### 修改默认模型
编辑 `config.py` 文件:
```python
class ProjectConfig:
    DEFAULT_ASR_MODEL = "SenseVoiceSmall"      # 语音识别模型
    DEFAULT_LLM_MODEL = "Qwen2.5-1.5B"        # 大语言模型
    DEFAULT_TTS_MODEL = "CosyVoice-300M-SFT"  # 语音合成模型
```

### 添加新模型路径
```python
class ModelConfig:
    LLM_MODELS = {
        "Qwen2.5-1.5B": f"{BASE_MODEL_DIR}/llm/Qwen2.5-1.5B-Instruct",
        "Qwen2.5-7B": f"{BASE_MODEL_DIR}/llm/Qwen2.5-7B-Instruct",  # 新增
    }
```

## 🐛 常见问题解决

### 1. 模型加载失败
```bash
# 检查模型路径
python config.py

# 检查模型文件
ls -la /pic/suziren/models/llm/Qwen2.5-1.5B-Instruct/
```

### 2. GPU内存不足
```bash
# 查看GPU状态
python launcher.py --list-gpus

# 使用空闲GPU
python launcher.py --script webui --gpu 1
```

### 3. 音频设备问题
```bash
# 安装音频依赖
pip install sounddevice

# 测试音频设备
python -c "import sounddevice as sd; print(sd.query_devices())"
```

## 📊 性能建议

- **推荐GPU**: GPU 1 (最空闲，24GB显存)
- **推荐模型**: Qwen2.5-1.5B (速度快，显存占用约6GB)
- **Web界面**: 适合批量处理和测试
- **实时对话**: 适合交互式使用

## 🎉 开始你的AI语音之旅！

现在一切都准备就绪，选择你喜欢的方式开始：

```bash
# 🌟 最简单 - Web界面
python launcher.py --script webui

# 🌟 最实用 - 实时对话
python launcher.py --script realtime

# 🌟 最智能 - 多模态交互
python launcher.py --script multimodal
```

## 📞 技术支持

如果遇到问题：
1. 查看 `使用指南.md` 详细说明
2. 运行 `python config.py` 检查配置
3. 使用 `--dry-run` 参数预览命令
4. 检查GPU状态和模型路径

祝你使用愉快！🚀✨
